'use client'

import { useTransition } from 'react'
import { addToWishlist, removeFromWishlist } from '../actions/wishlist'
import { useWishlist } from '../contexts/WishlistContext'

interface WishlistButtonProps {
  itemId: string
}

export default function WishlistButton({
  itemId
}: WishlistButtonProps) {
  const [isPending, startTransition] = useTransition()
  const {
    isInWishlist,
    addToWishlistOptimistic,
    removeFromWishlistOptimistic,
    revertOptimisticUpdate
  } = useWishlist()

  const optimisticIsInWishlist = isInWishlist(itemId)

  const handleWishlistToggle = () => {
    startTransition(async () => {
      const wasInWishlist = optimisticIsInWishlist

      // Optimistically update the UI
      if (wasInWishlist) {
        removeFromWishlistOptimistic(itemId)
      } else {
        addToWishlistOptimistic(itemId)
      }

      try {
        let result
        if (wasInWishlist) {
          result = await removeFromWishlist(itemId)
        } else {
          result = await addToWishlist(itemId)
        }

        // If the server action failed, revert the optimistic update
        if (!result.success) {
          revertOptimisticUpdate(itemId, wasInWishlist)
        }
      } catch (error) {
        // If there was an error, revert the optimistic update
        console.error('Wishlist action failed:', error)
        revertOptimisticUpdate(itemId, wasInWishlist)
      }
    })
  }

  return (
    <button
      onClick={handleWishlistToggle}
      disabled={isPending}
      className={`
        flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-200
        ${optimisticIsInWishlist 
          ? 'bg-red-50 border-red-200 text-red-700 hover:bg-red-100' 
          : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
        }
        ${isPending ? 'opacity-70 cursor-not-allowed' : 'cursor-pointer'}
      `}
    >
      <svg 
        width="20" 
        height="20" 
        viewBox="0 0 24 24" 
        fill={optimisticIsInWishlist ? "currentColor" : "none"}
        stroke="currentColor" 
        strokeWidth="2" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      >
        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
      </svg>
      {optimisticIsInWishlist ? 'Remove from Wishlist' : 'Add to Wishlist'}
      {isPending && (
        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
      )}
    </button>
  )
}
